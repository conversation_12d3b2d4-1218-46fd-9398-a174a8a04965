from blitzy_utils.common import blitzy_exponential_retry

from github import Github, GithubIntegration
from src.consts import GITHUB_APP_ID, GITHUB_PRIVATE_KEY
from src.scm_base.base_classes import BaseAppConnection


@blitzy_exponential_retry()
class GithubAppConnection(BaseAppConnection):
    def __init__(self):
        self.integration = GithubIntegration(GITHUB_APP_ID, GITHUB_PRIVATE_KEY)

    @blitzy_exponential_retry()
    def get_client(self, installation_id):
        """
        Retrieves a GitHub client instance using the installation access token.

        :param installation_id: Identifier of the GitHub App installation
        :type installation_id: int
        :return: Configured GitHub client instance
        """
        access_token = self.integration.get_access_token(int(installation_id)).token
        return Github(access_token)

    def update_access_token(self, new_access_token: str):
        """
        Update the stored access token.

        :param new_access_token: Fresh access token
        """
        pass

    def get_access_token(self, installation_id: str):
        """
        Generate and return an access token for a given installation ID.

        This method retrieves an access token associated with a specific installation ID
        by utilizing the integration instance assigned to the current object. The token
        is obtained using the integration method and is then returned as its string value.

        :param installation_id: The ID of the installation for which the access token
            is being generated.
        :type installation_id: int
        :return: A string representation of the access token associated with the given
            installation ID.
        :rtype: str
        """
        access_token = self.integration.get_access_token(int(installation_id)).token
        return access_token
