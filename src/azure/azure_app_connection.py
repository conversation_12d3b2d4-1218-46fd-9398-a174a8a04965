from azure.devops.connection import Connection
from msrest.authentication import BasicAuthentication

from blitzy_utils.common import blitzy_exponential_retry
from blitzy_utils.logger import logger
from src.consts import AZURE_BASE_URL
from src.scm_base.base_classes import BaseAppConnection
from src.service.azure_service import fetch_azure_secret


@blitzy_exponential_retry()
class AzureConnection(BaseAppConnection):
    def __init__(self, installation_id=None, access_token=None):
        """
        Initializes the AzureConnection. If access_token is not provided, it will be fetched using tenant_id.
        :param tenant_id: Azure AD tenant ID to fetch the access token for
        :param access_token: Optional pre-fetched access token
        """
        self.base_url = AZURE_BASE_URL or "https://dev.azure.com"
        if access_token:
            self.access_token = access_token
        elif installation_id:
            # Check if installation_id is null or empty
            if not installation_id or (isinstance(installation_id, str) and installation_id.strip() == ""):
                logger.error("Installation ID is null or empty in AzureConnection.__init__")
                raise ValueError("Installation ID cannot be null or empty")

            logger.debug(f"Fetching Azure secret for installation_id: {installation_id}")
            token_output = fetch_azure_secret(installation_id)
            self.access_token = token_output.accessToken
        else:
            self.access_token = None

    def get_client(self, organization, access_token=None):
        """
        Retrieves an Azure DevOps connection instance using Access Token.
        Access token expires every hour and needs to be refreshed.

        :param organization: Name of the Azure DevOps organization
        :type organization: str
        :param access_token: Optional fresh access token to use for this connection
        :type access_token: str
        :return: Configured Azure DevOps connection instance
        :rtype: Connection
        """
        # Use provided token or fall back to instance token
        token_to_use = access_token or self.access_token

        if not token_to_use:
            raise ValueError(
                "Access token is required. Token expires every hour and must be refreshed."
            )

        # Create credentials using access token
        credentials = BasicAuthentication("", token_to_use)

        # Build organization URL
        organization_url = f"{self.base_url}/{organization}"

        # Create and return connection
        return Connection(base_url=organization_url, creds=credentials)

    def update_access_token(self, new_access_token):
        """
        Updates the stored access token with a fresh one.

        :param new_access_token: The new access token to use
        :type new_access_token: str
        """
        self.access_token = new_access_token
