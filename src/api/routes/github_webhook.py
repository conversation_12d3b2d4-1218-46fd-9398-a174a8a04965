import hashlib
import hmac
from http.client import HTT<PERSON>Exception

from blitzy_utils.logger import logger
from flask import Blueprint, jsonify, request
from flask_utils.decorators import flask_pydantic_response
from flask_utils.models_config.model_utils import map_to_model

from src.api.handlers.installation_event_handler import \
    handle_installation_event
from src.api.handlers.pr_event_handler import handle_pr_event
from src.api.handlers.repository_event_handler import handle_repository_event
from src.api.models import GithubProjectRepoWithOrgOutput
from src.api.routes.operations import operations_bp
from src.api.routes.repositories import repositories_bp
from src.api.routes.secret_manager import secret_bp
from src.api.routes.users import users_bp
from src.azure.azure_app_service import AzureAppService
from src.consts import GITHUB_WEBHOOK_SECRET
from src.error.errors import ResourceNotFound
from src.service.azure_service import fetch_azure_secret
from src.service.github_installation_access_service import get_github_project_repo_by_id

SIGNATURE_HEADER_KEY = "X-Hub-Signature-256"
github_bp = Blueprint("github_bp", __name__, url_prefix="/v1/github")

github_bp.register_blueprint(users_bp)
github_bp.register_blueprint(secret_bp)
github_bp.register_blueprint(repositories_bp)
github_bp.register_blueprint(operations_bp)


@github_bp.route("/webhook", methods=["POST"])
def webhook():
    # Print all headers
    signature = request.headers.get(SIGNATURE_HEADER_KEY)

    verify_signature(request.data, signature)
    logger.debug("Verified signature.")

    event_type = request.headers.get("X-GitHub-Event")
    event_handler_factory(event_type, payload=request.json)

    return jsonify({"status": "success"}), 200


def verify_signature(payload_body: bytes, signature_header: str):
    """
    Verify that the payload was sent from GitHub by validating SHA256. Raise and return 403 if not authorized.

    :param payload_body: The original request body to verify.
    :param signature_header: The signature header received from GitHub.
    """
    if not signature_header:
        raise HTTPException(status_code=403, detail=f"{SIGNATURE_HEADER_KEY} header is missing!")

    expected_signature = generate_expected_signature(payload_body, GITHUB_WEBHOOK_SECRET)

    if not hmac.compare_digest(expected_signature, signature_header):
        raise HTTPException(status_code=403, detail="Signature mismatch! The request could not be verified.")


def generate_expected_signature(payload_body: bytes, secret: str) -> str:
    """Generate the expected HMAC SHA256 signature."""
    generated_hash = hmac.new(secret.encode("utf-8"), msg=payload_body, digestmod=hashlib.sha256)
    return "sha256=" + generated_hash.hexdigest()


def event_handler_factory(event: str, payload: dict):
    """Factory function to create event handlers."""
    logger.info(f"Handling github event {event}")
    if event == "installation":
        handle_installation_event(payload)
    elif event == "repository":
        handle_repository_event(payload)
    elif event == "pull_request":
        handle_pr_event(payload)
    else:
        logger.warning(f"No handler for event {event}")
        logger.debug(f"Payload: {payload}")


@github_bp.route("/repositories/<github_project_repo_id>/access-token", methods=["GET"])
@flask_pydantic_response
def get_access_token_by_installation_id(github_project_repo_id: str):
    """Get complete GitHub installation information with organization details using map_to_model pattern."""
    github_project_repo = get_github_project_repo_by_id(github_project_repo_id)
    if not github_project_repo:
        raise ResourceNotFound(f"No Github project repo found for id: {github_project_repo_id}")

    installation_id = github_project_repo.installation_id
    org_id = github_project_repo.azure_org_id
    secret = fetch_azure_secret(installation_id)
    access_token = secret.accessToken
    azure_service = AzureAppService()
    organization = azure_service._resolve_organization_id(org_id, access_token)

    response = map_to_model(github_project_repo, GithubProjectRepoWithOrgOutput)
    response.access_token = access_token
    response.organization = organization
    return response, 200
