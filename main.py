import signal
import sys
from types import FrameType

from blitzy_utils.logger import logger
from firebase_admin import initialize_app as initialize_firebase_admin
from flask import Flask
from flask_cors import CORS
from flask_utils.middleware.request_context import RequestContextMiddleware

from src.api.routes.auth import auth_bp
from src.api.routes.azure import azure_bp
from src.api.routes.azure_users import azure_users_bp
from src.api.routes.git import git_bp
from src.api.routes.github_installation import github_bp
from src.api.routes.hubspot import hubspot_bp
from src.api.routes.job import job_bp
from src.api.routes.project import project_bp
from src.api.routes.repository import repository_bp
from src.api.routes.stripe import stripe_bp
from src.api.routes.user import user_bp
from src.api.routes.utils import utils_bp
from src.consts import (
    AUTHORIZATION_HEADER_NAME,
    BASIC_AUTH_HEADER_NAME,
    ID_TOKEN_HEADER_NAME,
)

app = Flask(__name__)
# Configure CORS
CORS(
    app,
    resources={
        r"/*": {
            "origins": "*",
            "methods": ["GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"],
            "allow_headers": [
                "Content-Type",
                AUTHORIZATION_HEADER_NAME,
                ID_TOKEN_HEADER_NAME,
                BASIC_AUTH_HEADER_NAME,
            ],
            "expose_headers": ["x-correlation-id"],
        }
    },
)
initialize_firebase_admin()
app.register_blueprint(job_bp)
app.register_blueprint(auth_bp)
app.register_blueprint(utils_bp)
app.register_blueprint(project_bp)
app.register_blueprint(user_bp)
app.register_blueprint(stripe_bp)
app.register_blueprint(hubspot_bp)
app.register_blueprint(github_bp)
app.register_blueprint(repository_bp)
app.register_blueprint(azure_bp)
app.register_blueprint(azure_users_bp)
app.register_blueprint(git_bp)

middleware = RequestContextMiddleware(app, logger=logger)


def shutdown_handler(signal_int: int, frame: FrameType) -> None:
    logger.info(f"Caught Signal {signal.strsignal(signal_int)}")
    # Safely exit program
    sys.exit(0)


if __name__ == "__main__":
    # Running application locally, outside of a Google Cloud Environment
    # handles Ctrl-C termination
    logger.info("Starting Service...")
    signal.signal(signal.SIGINT, shutdown_handler)
    app.run(host="localhost", port=8080, debug=True)
else:
    # handles Cloud Run container termination
    logger.info("Starting Service..")
    signal.signal(signal.SIGTERM, shutdown_handler)
