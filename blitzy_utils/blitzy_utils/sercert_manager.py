import requests
from typing import Dict

from blitzy_utils.service_client import ServiceClient
from blitzy_utils.logger import logger


def get_github_token_from_secret_manager(user_id: str) -> Dict:
    with ServiceClient() as client:
        response = client.get("secret", f"/secret/{user_id}")
        response_output = response.json()
        if response.status_code != 200:
            logger.warning(f"Failed to get github token from secret manager. "
                           f"Status code: {response.status_code}, payload {response.text}")
            return {}
        return response_output


def save_github_credentials_to_secret_manager(user_id: str, access_token: str, code: str, installation_id: str,
                                              setup_action: str):
    with ServiceClient() as client:
        payload = {
            "userId": user_id,
            "accessToken": access_token,
            "code": code,
            "installationID": installation_id,
            "setupAction": setup_action,
        }
        response = client.post("secret", f"/secret", json=payload)
        if response.status_code > 299:
            logger.warning(
                f"Failed to save github credentials using secret manager. "
                f"Status code: {response.status_code}, payload {response.text}"
            )
        response.raise_for_status()


def get_github_token_from_github_handler(installation_id: str) -> Dict:
    with ServiceClient() as client:
        response = client.get("github", f"/v1/github/secret/{installation_id}")
        response_output = response.json()
        if response.status_code != 200:
            logger.warning(f"Failed to get github token from github-handler. "
                           f"Status code: {response.status_code}, payload {response.text}")
            return {}
        return response_output


def save_github_credentials_to_github_handler(access_token: str, code: str, installation_id: str,
                                              setup_action: str):
    with ServiceClient() as client:
        payload = {
            "accessToken": access_token,
            "code": code,
            "installationID": installation_id,
            "setupAction": setup_action,
        }
        response = client.post("github", f"/v1/github/secret", json=payload)
        if response.status_code > 299:
            logger.warning(
                f"Failed to save github credentials using github handler"
                f"Status code: {response.status_code}, payload {response.text}"
            )
        response.raise_for_status()


def get_github_access_token(client_id: str, client_secret: str, code: str):
    """Exchange the OAuth code for a user access token."""

    params = {
        "client_id": client_id,
        "client_secret": client_secret,
        "code": code,
    }

    headers = {
        "Accept": "application/json",
    }

    response = requests.post(
        "https://github.com/login/oauth/access_token",
        params=params,
        headers=headers
    )

    if response.status_code == 200:
        data = response.json()
        return data.get("access_token")
    else:
        logger.warning(f"Failed to get user token from github API. "
                       f"Status code: {response.status_code}, payload {response.text}")
        return None


def get_github_user(access_token):
    response = requests.get(
        "https://api.github.com/user",
        headers={
            "Authorization": f"Bearer {access_token}",
            "Accept": "application/vnd.github.v3+json"
        }
    )
    if response.status_code > 299:
        logger.warning(
            f"Failed to get user from github API. "
            f"Status code: {response.status_code}, payload {response.text}"
        )
    return response.json()
