import sys

import json

from datetime import datetime, timezone

import os
import logging
from enum import Enum
from pythonjsonlogger import jsonlogger
from pythonjsonlogger.json import JsonFormatter
from typing import Optional, Any, Dict


class LogLevel(Enum):
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"

    @classmethod
    def from_string(cls, level: str) -> "LogLevel":
        try:
            return cls[level.upper()]
        except KeyError:
            return cls.INFO


class LogConfig:
    """Configuration handler for logging"""

    @staticmethod
    def get_log_level() -> str:
        """Get log level from environment or default to INFO"""
        return os.getenv("LOG_LEVEL", "INFO").upper()

    @staticmethod
    def get_logging_config() -> dict:
        """Get all logging related configuration"""
        return {
            "level": LogConfig.get_log_level(),
            "format": os.getenv("LOG_FORMAT", "json"),
            "service_name": os.getenv("SERVICE_NAME", "undefined-service")
        }


class JsonFormatter(jsonlogger.JsonFormatter):
    def __init__(self):
        super().__init__()
        self._context = {}

    def format(self, record: logging.LogRecord) -> str:
        # Map Python logging levels to GCP severity levels
        SEVERITY_MAP = {
            logging.DEBUG: 'DEBUG',
            logging.INFO: 'INFO',
            logging.WARNING: 'WARNING',
            logging.ERROR: 'ERROR',
            logging.CRITICAL: 'CRITICAL'
        }

        # Extract extra fields first
        extra_fields = {}
        if hasattr(record, 'extra'):
            extra_fields = record.extra

        # Get context from record if available
        context = extra_fields.pop('_context', {}) if '_context' in extra_fields else {}

        # Create the base log record
        log_record = {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'severity': SEVERITY_MAP.get(record.levelno, 'DEFAULT'),  # Add GCP severity
            'level': record.levelname,
            'name': record.name,
            'message': record.getMessage(),
            'service': LogConfig.get_logging_config()['service_name']
        }

        if self._context:
            log_record.update(self._context)

        if extra_fields:
            log_record['data'] = extra_fields

        return json.dumps(log_record)


class BlitzyLogger:
    def __init__(self, name: str = ""):
        self.logger = logging.getLogger(name)
        self._context = {}
        self.config = LogConfig.get_logging_config()

        if not self.logger.handlers:
            self._setup_logger(name)

    def _setup_logger(self, name) -> None:
        self.logger = logging.getLogger(name)

        # Get log level from environment
        log_level_str = os.getenv('LOG_LEVEL', 'INFO').upper()
        log_level = getattr(logging, log_level_str, logging.INFO)  # Defaults to INFO if invalid level

        # Set level on logger
        self.logger.setLevel(log_level)

        # Handler for INFO and below (stdout)
        info_handler = logging.StreamHandler(sys.stdout)
        info_handler.setLevel(log_level)
        info_handler.addFilter(lambda record: record.levelno < logging.ERROR)

        # Handler for ERROR and above (stderr)
        error_handler = logging.StreamHandler(sys.stderr)
        error_handler.setLevel(logging.ERROR)

        # Set formatter for both handlers
        self._formatter = JsonFormatter()
        info_handler.setFormatter(self._formatter)
        error_handler.setFormatter(self._formatter)

        # Add both handlers
        self.logger.addHandler(info_handler)
        self.logger.addHandler(error_handler)

    def set_context(self, request_id: Optional[str] = None, correlation_id: Optional[str] = None,
                    user_id: Optional[str] = None, project_id: Optional[str] = None):
        # Create a copy of the existing context to preserve values
        new_context = self._context.copy()

        if request_id:
            new_context['request_id'] = request_id
        if correlation_id:
            new_context['correlation_id'] = correlation_id
        if user_id:
            new_context['user_id'] = user_id
        if project_id:
            new_context['project_id'] = project_id

        self._context = new_context
        self._formatter._context = self._context

    def info(self, message: str, **kwargs: Any) -> None:
        extra = kwargs if kwargs else None
        self.logger.info(message, extra={'extra': extra} if extra else None)

    def error(self, message: str, **kwargs: Any) -> None:
        extra = kwargs if kwargs else None
        self.logger.error(message, extra={'extra': extra} if extra else None)

    def warning(self, message: str, **kwargs: Any) -> None:
        extra = kwargs if kwargs else None
        self.logger.warning(message, extra={'extra': extra} if extra else None)

    def debug(self, message: str, **kwargs: Any) -> None:
        extra = kwargs if kwargs else None
        self.logger.debug(message, extra={'extra': extra} if extra else None)
