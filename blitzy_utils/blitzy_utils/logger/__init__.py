from typing import Any, Optional

from blitzy_utils.logger.base import BlitzyLogger

__default_logger = BlitzyLogger()


def info(message: str, **kwargs: Any) -> None:
    __default_logger.info(message, **kwargs)


def error(message: str, **kwargs: Any) -> None:
    __default_logger.error(message, **kwargs)


def warning(message: str, **kwargs: Any) -> None:
    __default_logger.warning(message, **kwargs)


def debug(message: str, **kwargs: Any) -> None:
    __default_logger.debug(message, **kwargs)


def set_context(request_id: Optional[str] = None, correlation_id: Optional[str] = None) -> None:
    __default_logger.set_context(request_id, correlation_id)


# Export the logger instance for advanced usage if needed
logger = __default_logger
